import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import RoutesGerenciamentoTermo from './RoutesGerenciamentoTermo';
import NotFound from 'fc/pages/NotFound';

const RoutesTermoReferencia = () => {
  return (
    <Switch>
      <Route path={UrlRouter.termoReferencia.gerenciamentoTermos.index} component={RoutesGerenciamentoTermo} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesTermoReferencia;
