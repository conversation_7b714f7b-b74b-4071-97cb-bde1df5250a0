const AccessPermission = {
  licitacao: {
    readPermission: 'listar_licitacao',
    writePermission: 'cadastrar_licitacao',
  },
  carona: {
    readPermission: 'listar_carona',
    writePermission: 'cadastrar_carona',
  },
  comissao: {
    readPermission: 'listar_comissao',
    writePermission: 'cadastrar_comissao',
  },
  dispensa: {
    readPermission: 'listar_dispensa',
    writePermission: 'cadastrar_dispensa',
  },
  inexigibilidade: {
    readPermission: 'listar_inexigibilidade',
    writePermission: 'cadastrar_inexigibilidade',
  },
  elementoDespesa: {
    readPermission: 'listar_elemento_despesa',
    writePermission: 'cadastrar_elemento_despesa',
  },
  entidadeExterna: {
    readPermission: 'listar_entidade_externa',
    writePermission: 'cadastrar_entidade_externa',
  },
  tipoLicitacao: {
    readPermission: 'listar_tipo_licitacao',
    writePermission: 'cadastrar_tipo_licitacao',
  },
  formaLicitacao: {
    readPermission: 'listar_forma_licitacao',
    writePermission: 'cadastrar_forma_licitacao',
  },
  formaPublicacao: {
    readPermission: 'listar_forma_publicacao',
    writePermission: 'cadastrar_forma_publicacao',
  },
  falha: {
    readPermission: 'listar_falha',
    writePermission: 'cadastrar_falha',
  },
  licitante: {
    readPermission: 'listar_licitante',
    writePermission: 'cadastrar_licitante',
  },
  feriado: {
    readPermission: 'listar_feriado',
    writePermission: 'cadastrar_feriado',
  },
  usuario: {
    readPermission: 'listar_usuario',
    writePermission: 'cadastrar_usuario',
  },
  usuarioAuditor: {
    readPermission: 'listar_usuario_auditor',
    writePermission: 'cadastrar_usuario_auditor',
  },
  paramArquivo: {
    readPermission: 'listar_param_arquivo',
    writePermission: 'cadastrar_param_arquivo',
  },
  variavelControle: {
    readPermission: 'listar_variavel_controle',
    writePermission: 'cadastrar_variavel_controle',
  },
  links: {
    readPermission: 'listar_links',
    writePermission: 'cadastrar_links',
  },
  painelMonitoramento: {
    writePermission: 'painel_monitoramento',
  },
  itemChecklist: {
    readPermission: 'listar_item_checklist',
    writePermission: 'cadastrar_item_checklist',
  },
  secaoChecklist: {
    readPermission: 'listar_secao_checklist',
    writePermission: 'cadastrar_secao_checklist',
  },
  paramAnaliseAutomatica: {
    readPermission: 'listar_param_analise_automatica',
    writePermission: 'cadastrar_param_analise_automatica',
  },
  mapaObras: {
    writePermission: 'mapa_obras',
  },
  processoAnalise: {
    readPermission: 'listar_processo_analise',
  },
  cadastrarTda: {
    readPermission: 'listar_tda',
    writePermission: 'cadastrar_tda',
  },
  requisicaoModificacao: {
    writePermission: 'requisicao_modificacao',
  },
  avaliarRequisicaoModificacao: {
    writePermission: 'avaliar_requisicao_modificacao',
  },
  rescisaoContratual: {
    writePermission: 'rescisao_contratual',
  },
  consultaMateriais: {
    readPermission: 'listar_consulta_materiais',
  },
  consultarLicitacao: {
    readPermission: 'listar_consultar_licitacao',
  },
  fonteRecurso: {
    readPermission: 'listar_fonte_recurso',
    writePermission: 'cadastrar_fonte_recurso',
  },
  fundamentacaoLegal: {
    readPermission: 'listar_fundamentacao_legal',
    writePermission: 'cadastrar_fundamentacao_legal',
  },
  gerenciamentoTermo: {
    readPermission: 'listar_gerenciamento_termo',
    writePermission: 'cadastrar_gerenciamento_termo',
  },
  grupoUsuario: {
    readPermission: 'listar_grupo_usuario',
    writePermission: 'cadastrar_grupo_usuario',
  },
  contrato: {
    readPermission: 'listar_contrato',
    writePermission: 'cadastrar_contrato',
  },
  aditivoContrato: {
    readPermission: 'listar_aditivo_contrato',
    writePermission: 'cadastrar_aditivo_contrato',
  },
  empenho: {
    readPermission: 'listar_empenho',
    writePermission: 'cadastrar_empenho',
  },
  geradorLinks: {
    readPermission: 'listar_gerador_links',
    writePermission: 'cadastrar_gerador_links',
  },
  manuais: {
    readPermission: 'listar_manuais',
    writePermission: 'cadastrar_manuais',
  },
  comunicadosDoe: {
    readPermission: 'listar_comunicados_doe',
    writePermission: 'cadastrar_comunicados_doe',
  },
  meusProcessos: {
    readPermission: 'listar_meus_processos',
    writePermission: 'cadastrar_meus_processos',
  },
  ocorrenciaLicitacao: {
    readPermission: 'listar_ocorrencia_licitacao',
    writePermission: 'cadastrar_ocorrencia_licitacao',
  },
  modalidadeLicitacao: {
    readPermission: 'listar_modalidade_licitacao',
    writePermission: 'cadastrar_modalidade_licitacao',
  },
  municipio: {
    readPermission: 'listar_municipio',
    writePermission: 'cadastrar_municipio',
  },
  parecerista: {
    readPermission: 'listar_parecerista',
    writePermission: 'cadastrar_parecerista',
  },
  responsavelEnte: {
    readPermission: 'listar_responsavel_ente',
    writePermission: 'cadastrar_responsavel_ente',
  },
  analiseProcessoView: {
    readPermission: 'listar_analise_processo',
  },
  alerta: {
    readPermission: 'listar_alerta',
    writePermission: 'cadastrar_alerta',
  },
  pessoaComissao: {
    readPermission: 'listar_pessoa_comissao',
    writePermission: 'cadastrar_pessoa_comissao',
  },
  historicoProcessos: {
    readPermission: 'listar_historico_processos',
  },
  funcaoRisco: {
    readPermission: 'listar_funcao_risco',
    writePermission: 'cadastrar_funcao_risco',
  },
  configuracoes: {
    readPermission: 'listar_configuracoes',
    writePermission: 'cadastrar_configuracoes',
  },
  monitoramentoAtosDiarioOficialView: {
    readPermission: 'listar_monitoramento_atos_diario_oficial',
  },
  edital: {
    readPermission: 'listar_edital',
    writePermission: 'cadastrar_edital',
  },
  analisarAlertaSecex: {
    writePermission: 'analisar_alerta_pendente',
  },
  analisarAlertaAuditorChefe: {
    writePermission: 'analisar_alerta_auditor_chefe',
  },
  obraEdificacaoView: {
    readPermission: 'listar_obra_edificacao_view',
  },
  obraMedicao: {
    readPermission: 'listar_obra_medicao',
    writePermission: 'cadastrar_obra_medicao',
  },
  painelAtosLicitacoes: {
    readPermission: 'listar_atos_licitacao_mateiro',
  },
  mapeamentoAtosLicitacoes: {
    readPermission: 'listar_mapeamento_atos_licitacoes',
    writePermission: 'cadastrar_mapeamento_atos_licitacoes',
  },
  boardLicitacoes: {
    readPermission: 'listar_board_licitacoes',
  },
  geoObras: {
    complexoObra: {
      readPermission: 'geoobra_listar_complexo_obra',
      writePermission: 'geoobra_cadastar_complexo_obra',
    },
    obra: {
      readPermission: 'geoobra_listar_obra',
      writePermission: 'geoobra_cadastrar_obra',
    },
    acompanhamento: {
      readPermission: 'geoobra_listar_acompanhamento_obra',
      writePermission: 'geoobra_cadastrar_acompanhamento_obra',
    },
    mapa3d: {
      readPermission: 'geoobra_mapa_3d',
    },
    denuncia: {
      readPermission: 'geoobra_listar_denuncia_obra',
    },
  },
  relatorioObra: {
    readPermission: 'listar_relatorio_obra',
    writePermission: 'cadastrar_relatorio_obra',
  },
  configuracoesEmail: {
    readPermission: 'listar_configuracoes_email',
    writePermission: 'cadastrar_configuracoes_email',
  },
  credenciamento: {
    readPermission: 'listar_credenciamento',
    writePermission: 'cadastrar_credenciamento',
  },
  credenciado: {
    readPermission: 'listar_credenciado',
    writePermission: 'cadastrar_credenciado',
  },
  entidadeTransferida: {
    readPermission: 'listar_entidade_transferida',
    writePermission: 'editar_entidade_transferida',
  },
  consultarContrato: {
    readPermission: 'listar_consultar_contrato',
  },
};
export default AccessPermission;
