const ApiEndpoints = {
  formaPublicacao: 'forma-publicacao',
  formaLicitacao: 'formas-licitacoes',
  tipoLicitacao: 'tipos-licitacao',
  elementoDespesa: 'elementos-despesa',
  entidadeExterna: 'entidades-externas',
  entidadeInternaExterna: 'entidades-internas-externas',
  fonteRecurso: 'fontes-recurso',
  linkConsulta: 'link-consulta',
  classificacaoAdministrativa: 'classificacoes-administrativas',
  esfera: 'esferas',
  poder: 'poderes',
  ente: 'entes',
  entidade: 'entidades',
  variavelControle: 'variaveis-controle',
  obrigatoriedadeArquivo: 'obrigatoriedade-arquivo',
  feriado: 'feriados',
  usuario: 'usuarios',
  usuarioAuditor: 'auditores-view',
  usuarioDiretor: 'diretores-view',
  licitante: 'licitantes',
  falha: 'falhas',
  statusSistema: 'status',
  item: 'itens',
  permissao: 'permissoes',
  grupoUsuario: 'grupos-usuario',
  comissao: 'comissoes',
  licitacao: 'licitacoes',
  termoReferencia: 'termos-referencia',
  termoReferenciaItem: 'termos-referencia-itens',
  termoReferenciaSecao: 'termos-referencia-secoes',
  itemLote: 'itens-lote',
  lote: 'lotes',
  dispensa: 'dispensas',
  carona: 'caronas',
  arquivoCarona: 'arquivos-carona',
  arquivoLicitacao: 'arquivos-licitacao',
  publicacao: 'publicacoes',
  parecerista: 'pareceristas',
  arquivoDispensa: 'arquivos-dispensa',
  arquivoInexigibilidade: 'arquivos-inexigibilidade',
  dispensaLicitante: 'dispensas-licitantes',
  vencedorLicitacao: 'vencedor-licitacao',
  fundamentacaoLegal: 'fundamentacoes-legais',
  contrato: 'contrato',
  modalidadeLicitacao: 'modalidades-licitacao',
  municipio: 'municipios',
  requisicaoModificacao: 'requisicao-modificacao',
  processoView: 'processo-view',
  itemLicitante: 'item-licitante',
  caronaLicitante: 'carona-licitante',
  inexigibilidadeLicitante: 'inexigibilidade-licitantes',
  ocorrenciaLicitacao: 'ocorrencias-licitacao',
  aditivoContrato: 'aditivo-contrato',
  inexigibilidade: 'inexigibilidades',
  secaoChecklist: 'secao-checklist',
  itemChecklist: 'item-checklist',
  responsavelEnte: 'responsavel-ente',
  objetoAnaliseAutomatica: 'objeto-analise-automatica',
  analiseProcessoView: 'analise-processo-view',
  obraTipo: 'obra-tipo',
  obraCategoria: 'obra-categoria',
  tda: 'tda',
  tdaLicitacao: 'tda-licitacao',
  tdaCarona: 'tda-carona',
  tdaDispensa: 'tda-dispensa',
  tdaInexigibilidade: 'tda-inexigibilidade',
  edificacao: 'edificacao',
  checklistLicitacao: 'checklist-licitacao',
  checklistCarona: 'checklist-carona',
  checklistDispensa: 'checklist-dispensa',
  checklistInexigibilidade: 'checklist-inexigibilidade',
  julgamentoRequisicao: 'julgamento-requisicao',
  selecioneAnaliseAuditoria: 'selecione-analise-auditoria',
  alertaAnalise: 'alerta-analise',
  alertaAnaliseEntidadeView: 'alerta-analise-entidade-view',
  alertaMensagem: 'alerta-mensagem',
  pessoaComissao: 'pessoa-comissao',
  historicoProcessosAuditoriaView: 'historico-processos-auditoria-view',
  notificacao: 'notificacoes',
  entrada: 'entrada',
  funcaoRisco: 'funcao-risco',
  monitoramentoAtosDiarioOficialView: 'monitoramento-atos-diario-oficial-view',
  atoDiarioOficialLicitacao: 'ato-diario-oficial-licitacao',
  editaisLicitacaoView: 'editais-licitacao-view',
  edital: 'edital',
  sentencaEdital: 'sentenca-edital',
  analisarAlertaSecex: 'analisar-alerta-pendente',
  empenhoContrato: 'empenho-contrato',
  obra: 'obra',
  obraMedicao: 'obra-medicao',
  obraEdificacaoView: 'obra-edificacao-view',
  configuracoes: 'configuracoes',
  classe: 'classes',
  divisao: 'divisoes',
  grupo: 'grupos',
  material: 'materiais',
  pdmCaracteristica: 'pdm-caracteristica',
  pdm: 'pdm',
  secaoCatalogo: 'secoes-catalogo',
  subClasse: 'sub-classes',
  riscoEntidade: 'risco-entidade',
  mapeamentoAtosLicitacoesView: 'mapeamento-atos-licitacoes-view',
  painelAtosLicitacoes: 'painel-atos-licitacao-view',
  fornecedoresContratadosView: 'fornecedores-contratados-view',
  tipoProcesso: 'tipo-processo',
  boardLicitacaoView: 'board-licitacao-view',
  geoObras: {
    obra: 'geo-obras/obras',
    diarios: 'geo-obras/diarios',
    auditorias: 'geo-obras/auditorias',
    acompanhamento: 'geo-obras/acompanhamento-obra-view',
    medicao: 'geo-obras/obras/#idObra/medicoes',
    mapa: 'geo-obras/mapa',
    mapa3D: 'geo-obras/mapa-3d',
    contrato: 'geo-obras/contrato',
    complexoObra: 'geo-obras/complexo-obra',
    denuncias: 'geo-obras/denuncias',
    situacaoObra: 'geo-obras/situacao-obra',
  },
  licitacaoSrpView: 'licitacao-srp-view',
  caronaView: 'carona-view',
  contratoView: 'contrato-view',
  consultarContrato: 'consultar-contrato',
  contratoCredView: 'contrato-credenciado-view',
  gerenciamentoTermoView: 'gerenciamento-termo-view',
  relatorioObra: 'geo-obras/relatorio-obra',
  catalogoObra: 'geo-obras/catalogo-obra',
  configuracoesEmail: 'configuracoes-email',
  processoLicitacaoView: 'processo-licitacao-view',
  credenciamento: 'credenciamentos',
  credenciado: 'credenciados',
  credenciadoItem: 'credenciado-item',
  tdaCredenciamento: 'tda-credenciamento',
  checklistCredenciamento: 'checklist-credenciamento',
  AnulacaoRevogacao: 'anulacao-revogacao',
  caronaEntidadeTransferidaView: 'carona-entidade-transferida-view',
  contratoEntidadeTransferidaView: 'contrato-entidade-transferida-view',
  credenciamentoEntidadeTransferidaView: 'credenciamento-entidade-transferida-view',
  dispensaEntidadeTransferidaView: 'dispensa-entidade-transferida-view',
  inexigibilidadeEntidadeTransferidaView: 'inexigibilidade-entidade-transferida-view',
  licitacaoEntidadeTransferidaView: 'licitacao-entidade-transferida-view',
  files: 'files',
};

export default ApiEndpoints;
