package br.gov.ac.tce.licon.entities;

import br.gov.ac.tce.licon.dtos.requests.ArquivoTermoReferenciaDTO;
import br.gov.ac.tce.licon.entities.enums.FormaPreenchimentoSecao;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.*;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Audited(withModifiedFlag = true)
@Table(name = "TERMO_REFERENCIA")
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
@AttributeOverride(name = "id", column = @Column(name = "ID_TERMO_REFERENCIA"))
public class TermoReferencia extends AbstractRequisicaoModificacaoIdentificavel {

    @ManyToOne(targetEntity = Usuario.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_USUARIO")
    private Usuario usuario;

    @NotNull
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(targetEntity = Entidade.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_ENTIDADE")
    private Entidade entidade;

    @Column(name = "DATA_CADASTRO")
    private LocalDateTime dataCadastro;

    @Column(name = "IDENTIFICADOR_PROCESSO")
    private String identificadorProcesso;

    @NotNull
    @Column(name = "FINALIZADO")
    private Boolean isFinalizado;

    @OneToMany(mappedBy = "termoReferencia", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "secoesCriadas")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<Secao> secoesCriadas;

    @OneToMany(mappedBy = "termoReferencia", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "secoes")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<TermoReferenciaSecao> secoes;

    @OneToMany(mappedBy = "termoReferencia", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference(value = "lotes")
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @NotAudited
    private List<Lote> lotes;

    @Column(name = "SRP")
    private Boolean srp;

    @Column(name = "OBRA_ENGENHARIA")
    private Boolean obraEngenharia;

    @Column(name = "TRES_CASAS_DECIMAIS")
    private Boolean tresCasasDecimais;

    @Column(name = "FORMA_PREENCHIMENTO_SECAO")
    @NotAudited
    @Enumerated(value = EnumType.STRING)
    private FormaPreenchimentoSecao formaPreenchimentoSecao;

    @Column(name = "TIPO")
    private String tipo;

    @Transient
    private List<ArquivoTermoReferenciaDTO> arquivosTemporarios;

    @NotAudited
    @Formula(value = "(CASE " +
            " WHEN BLOCKED_IN_PROCESS_REQ_MOD = 1 THEN 0" +
            " WHEN EXISTS (SELECT 1 FROM LICITACAO l WHERE l.ID_TERMO_REFERENCIA = ID_TERMO_REFERENCIA AND l.ID_STATUS_LICITACAO <> 'REMOVIDA') THEN 0" +
            " WHEN EXISTS (SELECT 1 FROM DISPENSA d WHERE d.ID_TERMO_REFERENCIA = ID_TERMO_REFERENCIA AND d.NM_STATUS_LICITACAO <> 'REMOVIDA') THEN 0" +
            " WHEN EXISTS (SELECT 1 FROM CARONA c WHERE c.ID_TERMO_REFERENCIA = ID_TERMO_REFERENCIA AND c.NM_STATUS_LICITACAO <> 'REMOVIDA') THEN 0" +
            " WHEN EXISTS (SELECT 1 FROM INEXIGIBILIDADE i WHERE i.ID_TERMO_REFERENCIA = ID_TERMO_REFERENCIA AND i.NM_STATUS_LICITACAO <> 'REMOVIDA') THEN 0" +
            " WHEN EXISTS (SELECT 1 FROM CREDENCIAMENTO cr WHERE cr.ID_TERMO_REFERENCIA = ID_TERMO_REFERENCIA AND cr.STATUS <> 'REMOVIDA') THEN 0" +
            " ELSE 1 END)")
    private Boolean disponivel;

    @NotAudited
    @Formula(value = "(SELECT CASE " +
            " WHEN EXISTS (SELECT TOP 1 1 FROM LICITACAO l WHERE l.ID_TERMO_REFERENCIA = ID_TERMO_REFERENCIA AND l.ID_STATUS_LICITACAO <> 'REMOVIDA' AND l.FASE = 'DIVULGACAO_PUBLICACAO_LICITACAO') THEN 1" +
            " ELSE 0 END)")
    private Boolean canCreateReqModificacao;

    @NotAudited
    @Column(name = "BLOCKED_IN_PROCESS_REQ_MOD")
    private Boolean blockedInProcessReqMod;

    @Override
    public String toString() {
        return this.identificadorProcesso;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        TermoReferencia that = (TermoReferencia) o;
        return Objects.equals(usuario, that.usuario) && Objects.equals(entidade, that.entidade) && Objects.equals(dataCadastro, that.dataCadastro) && Objects.equals(identificadorProcesso, that.identificadorProcesso) && Objects.equals(isFinalizado, that.isFinalizado) && Objects.equals(secoesCriadas, that.secoesCriadas) && Objects.equals(secoes, that.secoes) && Objects.equals(lotes, that.lotes) && Objects.equals(srp, that.srp) && Objects.equals(formaPreenchimentoSecao, that.formaPreenchimentoSecao);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), usuario, entidade, dataCadastro, identificadorProcesso, isFinalizado, secoesCriadas, secoes, lotes, srp, formaPreenchimentoSecao);
    }

    @Override
    public String titulo() {
        return this.toString();
    }
}
