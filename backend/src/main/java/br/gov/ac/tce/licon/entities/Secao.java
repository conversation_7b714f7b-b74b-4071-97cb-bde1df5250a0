package br.gov.ac.tce.licon.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Audited(withModifiedFlag = true)
@Table(name = "SECAO_TERMO_REFERENCIA")
@AttributeOverride(name = "id", column = @Column(name = "ID_SECAO"))
public class Secao extends AbstractIdentificavel {

    @NotNull
    @Column(name = "TITULO")
    private String titulo;

    @Column(name = "OBRIGATORIA")
    private Boolean obrigatoria;

    @Column(name = "ORDEM")
    private Integer ordem;

    @Column(name = "NOTAS_EXPLICATIVAS")
    private String notasExplicativas;

    @ManyToOne(targetEntity = TermoReferencia.class, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"}, allowSetters = true)
    @JoinColumn(name = "ID_TERMO_REFERENCIA")
    @JsonBackReference(value = "secoesCriadas")
    private TermoReferencia termoReferencia;

    @Column(name = "GERADA_TERMO")
    private Boolean geradaTermo;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Secao)) {
            return false;
        }
        Secao secao = (Secao) o;
        return Objects.equals(titulo, secao.titulo) && Objects.equals(obrigatoria, secao.obrigatoria) && Objects.equals(ordem, secao.ordem) && Objects.equals(notasExplicativas, secao.notasExplicativas) && Objects.equals(geradaTermo, secao.geradaTermo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(titulo, obrigatoria, ordem, notasExplicativas, geradaTermo);
    }
}
