package br.gov.ac.tce.licon.services;

import br.gov.ac.tce.licon.controllers.*;
import br.gov.ac.tce.licon.controllers.geoobras.CatalogoSicroSinapiController;
import br.gov.ac.tce.licon.controllers.geoobras.*;
import br.gov.ac.tce.licon.utils.PermissionCapsule;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Component
public class UserPermissionService {

    private static final String LISTAR_LICITACAO = "listar_licitacao";
    private static final String LISTAR_USUARIO = "listar_usuario";
    private static final String LISTAR_CARONA = "listar_carona";
    private static final String LISTAR_DISPENSA = "listar_dispensa";
    private static final String LISTAR_INEXIGIBILIDADE = "listar_inexigibilidade";
    private static final String LISTAR_ENTIDADE = "listar_entidade";
    private static final String LISTAR_PARAM_ARQUIVO = "listar_param_arquivo";
    private static final String LISTAR_MUNICIPIO = "listar_municipio";
    private static final String LISTAR_VENCEDOR_LICITACAO = "listar_vencedor_licitacao";
    private static final String LISTAR_GERENCIAMENTO_TERMO = "listar_gerenciamento_termo";
    private static final String LISTAR_TIPO_LICITACAO = "listar_tipo_licitacao";

    private static final String LISTAR_FONTE_RECURSO = "listar_fonte_recurso";
    private static final String LISTAR_ENTIDADE_EXTERNA = "listar_entidade_externa";
    private static final String LISTAR_LICITANTE = "listar_licitante";
    private static final String LISTAR_ARQUIVO_CARONA = "listar_arquivo_carona";
    private static final String LISTAR_ADITIVO_CONTRATO = "listar_aditivo_contrato";
    private static final String REQUISICAO_MODIFICACAO = "requisicao_modificacao";
    private static final String LISTAR_ITEM_LICITANTE = "listar_item_licitante";
    private static final String LISTAR_FUNDAMENTACAO_LEGAL = "listar_fundamentacao_legal";
    private static final String LISTAR_MODALIDADE_LICITACAO = "listar_modalidade_licitacao";
    private static final String CADASTRAR_LICITANTE = "cadastrar_licitante";
    private static final String LISTAR_ELEMENTO_DESPENSA = "listar_elemento_despesa";
    private static final String LISTAR_EDIFICACAO = "listar_edificacao";
    private static final String LISTAR_GRUPO_USUARIO = "listar_grupo_usuario";
    private static final String LISTAR_GRUPO = "listar_grupo";
    private static final String CADASTRAR_GERENCIAMENTO_TERMO = "cadastrar_gerenciamento_termo";
    private static final String LISTAR_PUBLICACAO = "listar_publicacao";
    private static final String LISTAR_OCORRENCIA_LICITACAO = "listar_ocorrencia_licitacao";
    private static final String LISTAR_CONSULTA_MATERIAIS = "listar_consulta_materiais";
    private static final String CADASTRAR_TDA_DISPENSA = "cadastrar_tda_dispensa";
    private static final String CADASTRAR_TDA_LICITACAO = "cadastrar_tda_licitacao";
    private static final String CADASTRAR_TDA_CARONA = "cadastrar_tda_carona";
    private static final String CADASTRAR_TDA_INEXIGIBILIDADE = "cadastrar_tda_inexigibilidade";
    private static final String LISTAR_USUARIO_AUDITOR = "listar_usuario_auditor";
    private static final String LISTAR_USUARIO_AUDITOR_VIEW = "listar_usuario_auditor_view";
    private static final String CADASTRAR_ADITIVO_CONTRATO = "cadastrar_aditivo_contrato";
    private static final String LISTAR_ALERTA = "listar_alerta";
    private static final String CADASTRAR_ALERTA = "cadastrar_alerta";
    private static final String CADASTRAR_ALERTA_MENSAGEM = "cadastrar_alerta_mensagem";
    private static final String LISTAR_ALERTA_MENSAGEM = "listar_alerta_mensagem";
    private static final String LISTAR_ALERTA_ANALISE_ENTIDADE = "listar_alerta_analise_entidade";
    private static final String LISTAR_ANALISE_PROCESSO = "listar_analise_processo";
    private static final String SELECIONE_ANALISE_AUDITORIA = "selecione_analise_auditoria";
    private static final String CADASTRAR_ARQUIVO_CARONA = "cadastrar_arquivo_carona";
    private static final String LISTAR_ARQUIVO_DISPENSA = "listar_arquivo_dispensa";
    private static final String CADASTRAR_ARQUIVO_DISPENSA = "cadastrar_arquivo_dispensa";
    private static final String LISTAR_ARQUIVO_INEXIGIBILIDADE = "listar_arquivo_inexigibilidade";
    private static final String CADASTRAR_ARQUIVO_INEXIGIBILIDADE = "cadastrar_arquivo_inexigibilidade";
    private static final String LISTAR_ARQUIVO_LICITACAO = "listar_arquivo_licitacao";
    private static final String CADASTRAR_ARQUIVO_LICITACAO = "cadastrar_arquivo_licitacao";
    private static final String LISTAR_ARQUIVO_OCORRENCIA_LICITACAO = "listar_arquivo_ocorrencia_licitacao";
    private static final String CADASTRAR_ARQUIVO_OCORRENCIA_LICITACAO = "cadastrar_arquivo_ocorrencia_licitacao";
    private static final String LISTAR_ATOS_DIARIO_OFICIAL_LICITACAO = "listar_atos_diario_oficial_licitacao";
    private static final String CADASTRAR_ATOS_DIARIO_OFICIAL_LICITACAO = "cadastrar_atos_diario_oficial_licitacao";
    private static final String CADASTRAR_CARONA = "cadastrar_carona";
    private static final String LISTAR_CARONA_LICITANTE = "listar_carona_licitante";
    private static final String CADASTRAR_CARONA_LICITANTE = "cadastrar_carona_licitante";
    private static final String CADASTRAR_ENTIDADE_EXTERNA = "cadastrar_entidade_externa";
    private static final String CADASTRAR_CHECKLIST_CARONA = "cadastrar_checklist_carona";
    private static final String LISTAR_CHECKLIST_CARONA = "listar_checklist_carona";
    private static final String LISTAR_CHECKLIST_DISPENSA = "listar_checklist_dispensa";
    private static final String CADASTRAR_CHECKLIST_DISPENSA = "cadastrar_checklist_dispensa";
    private static final String CADASTRAR_CHECKLIST_LICITACAO = "cadastrar_checklist_licitacao";
    private static final String LISTAR_CHECKLIST_LICITACAO = "listar_checklist_licitacao";
    private static final String LISTAR_CHECKLIST_INEXIGIBILIDADE = "listar_checklist_inexigibilidade";
    private static final String CADASTRAR_CHECKLIST_INEXIGIBILIDADE = "cadastrar_checklist_inexigibilidade";
    private static final String LISTAR_CLASSIFICACAO_ADMINISTRATIVA = "listar_classificacao_administrativa";
    private static final String CADASTRAR_CLASSIFICACAO_ADMINISTRATIVA = "cadastrar_classificacao_administrativa";
    private static final String CADASTRAR_COMISSAO = "cadastrar_comissao";
    private static final String LISTAR_COMISSAO = "listar_comissao";
    private static final String LISTAR_PESSOA_COMISSAO = "listar_pessoa_comissao";
    private static final String CADASTRAR_DISPENSA = "cadastrar_dispensa";
    private static final String LISTAR_DISPENSA_LICITANTE = "listar_dispensa_licitante";
    private static final String CADASTRAR_DISPENSA_LICITANTE = "cadastrar_dispensa_licitante";
    private static final String LISTAR_CONTRATO = "listar_contrato";
    private static final String CADASTRAR_CONTRATO = "cadastrar_contrato";
    private static final String LISTAR_PROCESSO_VIEW = "listar_processo_view";
    private static final String RESCISAO_CONTRATUAL = "rescisao_contratual";
    private static final String LISTAR_CONSULTA_PROCESSOS = "listar_consulta_processos";
    private static final String CADASTRAR_INEXIGIBILIDADE = "cadastrar_inexigibilidade";
    private static final String CADASTRAR_LICITACAO = "cadastrar_licitacao";
    private static final String CADASTRAR_EDIFICACAO = "cadastrar_edificacao";
    private static final String LISTAR_EDITAIS_LICITACAO = "listar_editais_licitacao";
    private static final String LISTAR_EDITAL = "listar_edital";
    private static final String CADASTRAR_EDITAL = "cadastrar_edital";
    private static final String LISTAR_SENTENCA_EDITAL = "listar_sentenca_edital";
    private static final String CADASTRAR_SENTENCA_EDITAL = "cadastrar_sentenca_edital";
    private static final String CADASTRAR_ELEMENTO_DESPESA = "cadastrar_elemento_despesa";
    private static final String LISTAR_ENTE = "listar_ente";
    private static final String CADASTRAR_ENTE = "cadastrar_ente";
    private static final String CADASTRAR_ENTIDADE = "cadastrar_entidade";
    private static final String ENTRADA = "entrada";
    private static final String LISTAR_ESFERA = "listar_esfera";
    private static final String CADASTRAR_ESFERA = "cadastrar_esfera";
    private static final String LISTAR_FALHA = "listar_falha";
    private static final String CADASTRAR_FALHA = "cadastrar_falha";
    private static final String LISTAR_FERIADO = "listar_feriado";
    private static final String CADASTRAR_FERIADO = "cadastrar_feriado";
    private static final String CADASTRAR_FONTE_RECURSO = "cadastrar_fonte_recurso";
    private static final String LISTAR_FORMA_LICITACAO = "listar_forma_licitacao";
    private static final String CADASTRAR_FORMA_LICITACAO = "cadastrar_forma_licitacao";
    private static final String LISTAR_FORMA_PUBLICACAO = "listar_forma_publicacao";
    private static final String CADASTRAR_FORMA_PUBLICACAO = "cadastrar_forma_publicacao";
    private static final String LISTAR_FUNCAO_RISCO = "listar_funcao_risco";
    private static final String CADASTRAR_FUNCAO_RISCO = "cadastrar_funcao_risco";
    private static final String CADASTRAR_FUNDAMENTACAO_LEGAL = "cadastrar_fundamentacao_legal";
    private static final String CADASTRAR_GRUPO_USUARIO = "cadastrar_grupo_usuario";
    private static final String LISTAR_PERMISSAO = "listar_permissao";
    private static final String LISTAR_HISTORICO_PROCESSOS = "listar_historico_processos";
    private static final String LISTAR_INEXIGIBILIDADE_LICITANTE = "listar_inexigibilidade_licitante";
    private static final String CADASTRAR_INEXIGIBILIDADE_LICITANTE = "cadastrar_inexigibilidade_licitante";
    private static final String LISTAR_ITEM_CHECKLIST = "listar_item_checklist";
    private static final String CADASTRAR_ITEM_CHECKLIST = "cadastrar_item_checklist";
    private static final String LISTAR_SECAO_CHECKLIST = "listar_secao_checklist";
    private static final String AVALIAR_REQUISICAO_MODIFICACAO = "avaliar_requisicao_modificacao";
    private static final String LISTAR_PARECERISTA = "listar_parecerista";
    private static final String CADASTRAR_PARECERISTA = "cadastrar_parecerista";
    private static final String LISTAR_OBRA_CATEGORIA = "listar_obra_categoria";
    private static final String LISTAR_OBRA_EDIFICACAO_VIEW = "listar_obra_edificacao_view";
    private static final String CADASTRAR_OBRA_CATEGORIA = "cadastrar_obra_categoria";
    private static final String LISTAR_OBRA_MEDICAO = "listar_obra_medicao";
    private static final String CADASTRAR_OBRA_MEDICAO = "cadastrar_obra_medicao";
    private static final String LISTAR_OBRA_TIPO = "listar_obra_tipo";
    private static final String CADASTRAR_OBRA_TIPO = "cadastrar_obra_tipo";
    private static final String LISTAR_GERADOR_LINKS = "listar_gerador_links";
    private static final String CADASTRAR_GERADOR_LINKS = "cadastrar_gerador_links";
    private static final String CADASTRAR_MODALIDADE_LICITACAO = "cadastrar_modalidade_licitacao";
    private static final String LISTAR_MONITORAMENTO_ATOS_DIARIO_OFICIAL = "listar_monitoramento_atos_diario_oficial";
    private static final String CADASTRAR_MUNICIPIO = "cadastrar_municipio";
    private static final String CADASTRAR_PARAM_ANALISE_AUTOMATICA = "cadastrar_param_analise_automatica";
    private static final String LISTAR_PARAM_ANALISE_AUTOMATICA = "listar_param_analise_automatica";
    private static final String CADASTRAR_PARAM_ARQUIVO = "cadastrar_param_arquivo";
    private static final String LISTAR_OBRA = "listar_obra";
    private static final String CADASTRAR_OCORRENCIA_LICITACAO = "cadastrar_ocorrencia_licitacao";
    private static final String CADASTRAR_PESSOA_COMISSAO = "cadastrar_pessoa_comissao";
    private static final String CADASTRAR_PERMISSAO = "cadastrar_permissao";
    private static final String LISTAR_PODER = "listar_poder";
    private static final String CADASTRAR_PODER = "cadastrar_poder";
    private static final String CADASTRAR_PUBLICACAO = "cadastrar_publicacao";
    private static final String LISTAR_RESPONSAVEL_ENTE = "listar_responsavel_ente";
    private static final String CADASTRAR_RESPONSAVEL_ENTE = "cadastrar_responsavel_ente";
    private static final String CADASTRAR_SECAO_CHECKLIST = "cadastrar_secao_checklist";

    private static final String LISTAR_SISTEMA = "listar_sistema";
    private static final String CADASTRAR_SISTEMA = "cadastrar_sistema";
    private static final String CADASTRAR_TDA = "cadastrar_tda";
    private static final String LISTAR_TDA = "listar_tda";
    private static final String LISTAR_TDA_DISPENSA = "listar_tda_dispensa";
    private static final String LISTAR_TDA_INEXIGIBILIDADE = "listar_tda_inexigibilidade";
    private static final String LISTAR_TDA_CARONA = "listar_tda_carona";
    private static final String LISTAR_TDA_LICITACAO = "listar_tda_licitacao";
    private static final String CADASTRAR_ITEM_LICITANTE = "cadastrar_item_licitante";
    private static final String CADASTRAR_TIPO_LICITACAO = "cadastrar_tipo_licitacao";
    private static final String CADASTRAR_USUARIO = "cadastrar_usuario";
    private static final String LISTAR_VARIAVEL_CONTROLE = "listar_variavel_controle";
    private static final String CADASTRAR_VARIAVEL_CONTROLE = "cadastrar_variavel_controle";
    private static final String CADASTRAR_VENCEDOR_LICITACAO = "cadastrar_vencedor_licitacao";
    private static final String ANALISAR_ALERTA_PENDENTE = "analisar_alerta_pendente";
    private static final String ANALISAR_ALERTA_AUDITOR_CHEFE = "analisar_alerta_auditor_chefe";
    private static final String LISTAR_EMPENHO = "listar_empenho";
    private static final String CADASTRAR_EMPENHO = "cadastrar_empenho";
    private static final String LISTAR_CONFIGURACOES = "listar_configuracoes";
    private static final String CADASTRAR_CONFIGURACOES = "cadastrar_configuracoes";
    private static final String LISTAR_UNIDADE_MEDIDA = "listar_unidade_medida";
    private static final String LISTAR_CLASSE = "listar_classe";
    private static final String LISTAR_DIVISAO = "listar_divisao";
    private static final String LISTAR_PDM_CARACTERISTICA = "listar_pdm_caracteristica";
    private static final String LISTAR_PDM = "listar_pdm";
    private static final String LISTAR_SECAO_CATALOGO = "listar_secao_catalogo";
    private static final String LISTAR_SUB_CLASSE = "listar_sub_classe";
    private static final String LISTAR_RISCO_ENTIDADE = "listar_risco_entidade";
    private static final String CADASTRAR_RISCO_ENTIDADE = "cadastrar_risco_entidade";
    private static final String CADASTRAR_OBRA = "cadastrar_obra";
    private static final String LISTAR_MAPEAMENTO_ATOS_LICITACOES = "listar_mapeamento_atos_licitacoes";
    private static final String CADASTRAR_MAPEAMENTO_ATOS_LICITACOES = "cadastrar_mapeamento_atos_licitacoes";
    private static final String LISTAR_TIPO_PROCESSO = "listar-tipo-processo";
    private static final String CADASTRAR_TIPO_PROCESSO = "cadastrar-tipo-processo";
    private static final String LISTAR_FORNECEDORES_CONTRATADOS_VIEW = "listar_fornecedores_contratados";
    private static final String LISTAR_ALERTAS_VIEW = "listar_alertas_view";
    private static final String LISTAR_BOARD_LICITACAO_VIEW = "listar_board_licitacoes";
    private static final String LISTAR_LICITACAO_SRP_VIEW = "listar_licitacoes_srp";
    private static final String LISTAR_RELATORIO_OBRA = "listar_relatorio_obra";
    private static final String CADASTRAR_RELATORIO_OBRA = "cadastrar_relatorio_obra";
    private static final String LISTAR_CONFIGURACOES_EMAIL = "listar_configuracoes_email";
    private static final String CADASTRAR_CONFIGURACOES_EMAIL = "cadastrar_configuracoes_email";
    private static final String GEOOBRA_LISTAR_OBRA = "geoobra_listar_obra";
    private static final String GEOOBRA_CADASTRAR_OBRA = "geoobra_cadastrar_obra";
    private static final String GEOOBRA_LISTAR_DENUNCIA_OBRA = "geoobra_listar_denuncia_obra";
    private static final String LISTAR_CREDENCIAMENTO = "listar_credenciamento";
    private static final String CADASTRAR_CREDENCIAMENTO = "cadastrar_credenciamento";
    private static final String LISTAR_CREDENCIADO = "listar_credenciado";
    private static final String CADASTRAR_CREDENCIADO = "cadastrar_credenciado";
    private static final String LISTAR_CREDENCIADO_ITEM = "listar_credenciado_item";
    private static final String CADASTRAR_CREDENCIADO_ITEM = "cadastrar_credenciado_item";
    private static final String LISTAR_TDA_CREDENCIAMENTO = "listar_tda_credenciamento";
    private static final String CADASTRAR_TDA_CREDENCIAMENTO = "cadastrar_tda_credenciamento";
    private static final String LISTAR_CHECKLIST_CREDENCIAMENTO = "listar_checklist_credenciamento";
    private static final String CADASTRAR_CHECKLIST_CREDENCIAMENTO = "cadastrar_checklist_credenciamento";
    private static final String GEOOBRA_LISTAR_ACOMPANHAMENTO_OBRA = "geoobra_listar_acompanhamento_obra";
    private static final String GEOOBRA_CADASTRAR_ACOMPANHAMENTO_OBRA = "geoobra_cadastrar_acompanhamento_obra";
    private static final String GEOOBRA_LISTAR_MAPA_3D =  "geoobra_mapa_3d";
    private static final String LISTAR_ATOS_LICITACAO_MATEIRO = "listar_atos_licitacao_mateiro";
    private static final String LISTAR_ANULACAO_REVOGACAO = "listar_anulacao_revogacao";
    private static final String CADASTRAR_ANULACAO_REVOGACAO = "cadastrar_anulacao_revogacao";
    private static final String LISTAR_ARQUIVOS_ANULACAO_REVOGACAO = "listar_arquivos_anulacao_revogacao";
    private static final String CADASTRAR_ARQUIVOS_ANULACAO_REVOGACAO = "cadastrar_arquivos_anulacao_revogacao";
    private static final String LISTAR_ENTIDADE_TRANSFERIDA = "listar_entidade_transferida";
    private static final String EDITAR_ENTIDADE_TRANSFERIDA = "editar_entidade_transferida";
    private static final String GEOOBRA_LISTAR_COMPLEXO_OBRA = "geoobra_listar_complexo_obra";
    private static final String GEOOBRA_CADASTRAR_COMPLEXO_OBRA = "geoobra_cadastar_complexo_obra";

    private static Map<String, PermissionCapsule> permissionMapping = ImmutableMap.<String, PermissionCapsule>builder()
            .put(AditivoContratoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ADITIVO_CONTRATO).writePermission(CADASTRAR_ADITIVO_CONTRATO).build())
            .put(AlertaAnaliseController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ALERTA).writePermission(CADASTRAR_ALERTA).associated(ImmutableList.of(ANALISAR_ALERTA_AUDITOR_CHEFE, LISTAR_LICITACAO, LISTAR_DISPENSA, LISTAR_CARONA, LISTAR_INEXIGIBILIDADE, LISTAR_CREDENCIAMENTO, LISTAR_USUARIO, CADASTRAR_ALERTA_MENSAGEM, LISTAR_ALERTA_ANALISE_ENTIDADE, LISTAR_TDA_INEXIGIBILIDADE, LISTAR_TDA_CARONA, LISTAR_TDA_DISPENSA, LISTAR_TDA_LICITACAO, LISTAR_TDA, LISTAR_TDA_CREDENCIAMENTO)).build())
            .put(AlertaAnaliseEntidadeViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ALERTA_ANALISE_ENTIDADE).associated(ImmutableList.of(LISTAR_USUARIO)).build())
            .put(AlertaMensagemController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ALERTA_MENSAGEM).writePermission(CADASTRAR_ALERTA_MENSAGEM).build())
            .put(AnalisarAlertaSecexController.class.getName(), PermissionCapsule.builder().writePermission(ANALISAR_ALERTA_PENDENTE).associated(ImmutableList.of(LISTAR_ALERTA, LISTAR_LICITACAO, LISTAR_DISPENSA, LISTAR_CARONA, LISTAR_INEXIGIBILIDADE, LISTAR_CREDENCIAMENTO, CADASTRAR_TDA_CARONA, CADASTRAR_TDA_DISPENSA, CADASTRAR_TDA_INEXIGIBILIDADE, CADASTRAR_TDA_LICITACAO, CADASTRAR_TDA_CREDENCIAMENTO)).build())
            .put(AnaliseProcessoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ANALISE_PROCESSO).associated(ImmutableList.of(LISTAR_ENTIDADE, SELECIONE_ANALISE_AUDITORIA, LISTAR_USUARIO, LISTAR_CONFIGURACOES, CADASTRAR_ARQUIVO_LICITACAO, LISTAR_USUARIO_AUDITOR_VIEW)).build())
            .put(ArquivoCaronaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ARQUIVO_CARONA).writePermission(CADASTRAR_ARQUIVO_CARONA).build())
            .put(ArquivoDispensaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ARQUIVO_DISPENSA).writePermission(CADASTRAR_ARQUIVO_DISPENSA).build())
            .put(ArquivoInexigibilidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ARQUIVO_INEXIGIBILIDADE).writePermission(CADASTRAR_ARQUIVO_INEXIGIBILIDADE).build())
            .put(ArquivoLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ARQUIVO_LICITACAO).writePermission(CADASTRAR_ARQUIVO_LICITACAO).build())
            .put(ArquivoOcorrenciaLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ARQUIVO_OCORRENCIA_LICITACAO).writePermission(CADASTRAR_ARQUIVO_OCORRENCIA_LICITACAO).build())
            .put(AtoDiarioOficialLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ATOS_DIARIO_OFICIAL_LICITACAO).writePermission(CADASTRAR_ATOS_DIARIO_OFICIAL_LICITACAO).build())
            .put(BoardLicitacaoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_BOARD_LICITACAO_VIEW).associated(ImmutableList.of(LISTAR_LICITACAO, LISTAR_ENTIDADE, LISTAR_GERENCIAMENTO_TERMO, LISTAR_TIPO_LICITACAO, LISTAR_FORMA_LICITACAO, LISTAR_COMISSAO, LISTAR_FONTE_RECURSO, LISTAR_OCORRENCIA_LICITACAO, LISTAR_PUBLICACAO, LISTAR_PARAM_ARQUIVO, LISTAR_PARECERISTA, LISTAR_FORMA_PUBLICACAO, LISTAR_ARQUIVO_LICITACAO, LISTAR_EDIFICACAO, LISTAR_OBRA_CATEGORIA, LISTAR_OBRA_TIPO, LISTAR_MODALIDADE_LICITACAO, LISTAR_CONTRATO, LISTAR_ADITIVO_CONTRATO, LISTAR_BOARD_LICITACAO_VIEW)).build())
            .put(ProcessoLicitacaoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONSULTA_PROCESSOS).associated(ImmutableList.of(LISTAR_LICITACAO, LISTAR_ENTIDADE, LISTAR_PARAM_ARQUIVO, LISTAR_ADITIVO_CONTRATO)).build())
            .put(CaronaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CARONA).writePermission(CADASTRAR_CARONA).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_USUARIO, LISTAR_TIPO_LICITACAO, LISTAR_FONTE_RECURSO, LISTAR_LICITANTE, LISTAR_ARQUIVO_CARONA, LISTAR_CARONA_LICITANTE, LISTAR_ENTIDADE_EXTERNA, LISTAR_MUNICIPIO, LISTAR_PARAM_ARQUIVO, LISTAR_VENCEDOR_LICITACAO, LISTAR_LICITACAO, LISTAR_GERENCIAMENTO_TERMO, REQUISICAO_MODIFICACAO, CADASTRAR_ENTIDADE_EXTERNA, CADASTRAR_LICITANTE, LISTAR_LICITACAO_SRP_VIEW, CADASTRAR_ANULACAO_REVOGACAO, LISTAR_CONTRATO, LISTAR_ADITIVO_CONTRATO)).build())
            .put(CaronaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CARONA).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_USUARIO, LISTAR_TIPO_LICITACAO, LISTAR_FONTE_RECURSO, LISTAR_LICITANTE, LISTAR_ARQUIVO_CARONA, LISTAR_CARONA_LICITANTE, LISTAR_ENTIDADE_EXTERNA, LISTAR_MUNICIPIO, LISTAR_PARAM_ARQUIVO, LISTAR_VENCEDOR_LICITACAO, LISTAR_LICITACAO, LISTAR_GERENCIAMENTO_TERMO, REQUISICAO_MODIFICACAO, CADASTRAR_ENTIDADE_EXTERNA, CADASTRAR_LICITANTE, LISTAR_LICITACAO_SRP_VIEW)).build())
            .put(GerenciamentoTermoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GERENCIAMENTO_TERMO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_CONSULTA_MATERIAIS, LISTAR_GRUPO, LISTAR_UNIDADE_MEDIDA, LISTAR_CLASSE, LISTAR_DIVISAO, LISTAR_PDM_CARACTERISTICA, LISTAR_PDM, LISTAR_SECAO_CATALOGO, LISTAR_SUB_CLASSE)).build())
            .put(CaronaLicitanteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CARONA_LICITANTE).writePermission(CADASTRAR_CARONA_LICITANTE).associated(ImmutableList.of(LISTAR_ITEM_LICITANTE)).build())
            .put(ChecklistCaronaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CHECKLIST_CARONA).writePermission(CADASTRAR_CHECKLIST_CARONA).build())
            .put(ChecklistDispensaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CHECKLIST_DISPENSA).writePermission(CADASTRAR_CHECKLIST_DISPENSA).build())
            .put(ChecklistLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CHECKLIST_LICITACAO).writePermission(CADASTRAR_CHECKLIST_LICITACAO).build())
            .put(ChecklistInexigibilidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CHECKLIST_INEXIGIBILIDADE).writePermission(CADASTRAR_CHECKLIST_INEXIGIBILIDADE).build())
            .put(ClasseController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CLASSE).build())
            .put(ClassificacaoAdministrativaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CLASSIFICACAO_ADMINISTRATIVA).writePermission(CADASTRAR_CLASSIFICACAO_ADMINISTRATIVA).build())
            .put(ComissaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_COMISSAO).writePermission(CADASTRAR_COMISSAO).associated(ImmutableList.of(LISTAR_USUARIO, LISTAR_ENTIDADE, LISTAR_PESSOA_COMISSAO)).build())
            .put(ConfiguracoesController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONFIGURACOES).writePermission(CADASTRAR_CONFIGURACOES).associated(ImmutableList.of(CADASTRAR_RISCO_ENTIDADE)).build())
            .put(DispensaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_DISPENSA).writePermission(CADASTRAR_DISPENSA).associated(ImmutableList.of(LISTAR_FONTE_RECURSO, LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_ARQUIVO_DISPENSA, LISTAR_DISPENSA_LICITANTE, LISTAR_FUNDAMENTACAO_LEGAL, LISTAR_PARAM_ARQUIVO, LISTAR_GERENCIAMENTO_TERMO, REQUISICAO_MODIFICACAO, CADASTRAR_LICITANTE, LISTAR_USUARIO, CADASTRAR_ANULACAO_REVOGACAO, LISTAR_CONTRATO, LISTAR_ADITIVO_CONTRATO)).build())
            .put(DispensaLicitanteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_DISPENSA_LICITANTE).writePermission(CADASTRAR_DISPENSA_LICITANTE).associated(ImmutableList.of(LISTAR_ITEM_LICITANTE)).build())
            .put(DivisaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_DIVISAO).build())
            .put(ContratoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONTRATO).writePermission(CADASTRAR_CONTRATO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_USUARIO, LISTAR_FONTE_RECURSO, LISTAR_ELEMENTO_DESPENSA, LISTAR_VENCEDOR_LICITACAO, LISTAR_PROCESSO_VIEW, REQUISICAO_MODIFICACAO, RESCISAO_CONTRATUAL, LISTAR_ADITIVO_CONTRATO, LISTAR_PARAM_ARQUIVO, LISTAR_INEXIGIBILIDADE, LISTAR_CONSULTA_PROCESSOS, CADASTRAR_INEXIGIBILIDADE, LISTAR_MODALIDADE_LICITACAO, CADASTRAR_CARONA, CADASTRAR_DISPENSA, CADASTRAR_LICITACAO, LISTAR_ENTIDADE_EXTERNA, LISTAR_CREDENCIADO_ITEM)).build())
            .put(ContratoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONTRATO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_USUARIO, LISTAR_FONTE_RECURSO, LISTAR_ELEMENTO_DESPENSA, LISTAR_VENCEDOR_LICITACAO, LISTAR_PROCESSO_VIEW, REQUISICAO_MODIFICACAO, RESCISAO_CONTRATUAL, LISTAR_ADITIVO_CONTRATO, LISTAR_PARAM_ARQUIVO, LISTAR_INEXIGIBILIDADE, LISTAR_CONSULTA_PROCESSOS, LISTAR_MODALIDADE_LICITACAO, LISTAR_ENTIDADE_EXTERNA)).build())
            .put(ConsultarContratoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONTRATO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_USUARIO, LISTAR_FONTE_RECURSO, LISTAR_ELEMENTO_DESPENSA, LISTAR_VENCEDOR_LICITACAO, LISTAR_PROCESSO_VIEW, REQUISICAO_MODIFICACAO, RESCISAO_CONTRATUAL, LISTAR_ADITIVO_CONTRATO, LISTAR_PARAM_ARQUIVO, LISTAR_INEXIGIBILIDADE, LISTAR_CONSULTA_PROCESSOS, LISTAR_MODALIDADE_LICITACAO, LISTAR_ENTIDADE_EXTERNA)).build())
            .put(ContratoCredViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONTRATO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_USUARIO, LISTAR_FONTE_RECURSO, LISTAR_ELEMENTO_DESPENSA, LISTAR_VENCEDOR_LICITACAO, LISTAR_PROCESSO_VIEW, REQUISICAO_MODIFICACAO, RESCISAO_CONTRATUAL, LISTAR_ADITIVO_CONTRATO, LISTAR_PARAM_ARQUIVO, LISTAR_INEXIGIBILIDADE, LISTAR_CONSULTA_PROCESSOS, LISTAR_MODALIDADE_LICITACAO, LISTAR_ENTIDADE_EXTERNA)).build())
            .put(EdificacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_EDIFICACAO).writePermission(CADASTRAR_EDIFICACAO).build())
            .put(EditaisLicitacaoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_EDITAIS_LICITACAO).build())
            .put(EditalController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_EDITAL).writePermission(CADASTRAR_EDITAL).associated(ImmutableList.of(LISTAR_EDITAIS_LICITACAO, LISTAR_SENTENCA_EDITAL)).build())
            .put(ElementoDespesaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ELEMENTO_DESPENSA).writePermission(CADASTRAR_ELEMENTO_DESPESA).build())
            .put(EmpenhoContratoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_EMPENHO).writePermission(CADASTRAR_EMPENHO).build())
            .put(EnteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ENTE).writePermission(CADASTRAR_ENTE).build())
            .put(EntidadeExternaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ENTIDADE_EXTERNA).writePermission(CADASTRAR_ENTIDADE_EXTERNA).associated(ImmutableList.of(LISTAR_MUNICIPIO)).build())
            .put(EntidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ENTIDADE).writePermission(CADASTRAR_ENTIDADE).build())
            .put(EntradaController.class.getName(), PermissionCapsule.builder().readPermission(ENTRADA).writePermission(ENTRADA).associated(ImmutableList.of(LISTAR_GRUPO_USUARIO, LISTAR_ALERTA_ANALISE_ENTIDADE, LISTAR_FORNECEDORES_CONTRATADOS_VIEW, LISTAR_ALERTAS_VIEW)).build())
            .put(EsferaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ESFERA).writePermission(CADASTRAR_ESFERA).build())
            .put(FalhaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FALHA).writePermission(CADASTRAR_FALHA).build())
            .put(FeriadoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FERIADO).writePermission(CADASTRAR_FERIADO).build())
            .put(FonteRecursoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FONTE_RECURSO).writePermission(CADASTRAR_FONTE_RECURSO).build())
            .put(FormaLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FORMA_LICITACAO).writePermission(CADASTRAR_FORMA_LICITACAO).build())
            .put(FormaPublicacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FORMA_PUBLICACAO).writePermission(CADASTRAR_FORMA_PUBLICACAO).build())
            .put(FuncaoRiscoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FUNCAO_RISCO).writePermission(CADASTRAR_FUNCAO_RISCO).associated(ImmutableList.of(LISTAR_TIPO_PROCESSO)).build())
            .put(FundamentacaoLegalController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FUNDAMENTACAO_LEGAL).writePermission(CADASTRAR_FUNDAMENTACAO_LEGAL).build())
            .put(GrupoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GRUPO).build())
            .put(GrupoUsuarioController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GRUPO_USUARIO).writePermission(CADASTRAR_GRUPO_USUARIO).associated(ImmutableList.of(LISTAR_USUARIO, LISTAR_PERMISSAO)).build())
            .put(HistoricoProcessosAuditoriaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_HISTORICO_PROCESSOS).associated(ImmutableList.of(LISTAR_ANALISE_PROCESSO, LISTAR_ENTIDADE, LISTAR_USUARIO)).build())
            .put(InexigibilidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_INEXIGIBILIDADE).writePermission(CADASTRAR_INEXIGIBILIDADE).associated(ImmutableList.of(LISTAR_FONTE_RECURSO, LISTAR_LICITANTE, LISTAR_ARQUIVO_INEXIGIBILIDADE, LISTAR_FUNDAMENTACAO_LEGAL, LISTAR_INEXIGIBILIDADE_LICITANTE, LISTAR_ENTIDADE, LISTAR_PARAM_ARQUIVO, REQUISICAO_MODIFICACAO, LISTAR_GERENCIAMENTO_TERMO, CADASTRAR_LICITANTE, CADASTRAR_ANULACAO_REVOGACAO, LISTAR_CONTRATO, LISTAR_ADITIVO_CONTRATO)).build())
            .put(InexigibilidadeLicitanteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_INEXIGIBILIDADE_LICITANTE).writePermission(CADASTRAR_INEXIGIBILIDADE_LICITANTE).build())
            .put(ItemChecklistController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ITEM_CHECKLIST).writePermission(CADASTRAR_ITEM_CHECKLIST).associated(ImmutableList.of(LISTAR_SECAO_CHECKLIST)).build())
            .put(JulgamentoRequisicaoController.class.getName(), PermissionCapsule.builder().writePermission(AVALIAR_REQUISICAO_MODIFICACAO).build())
            .put(LoteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GERENCIAMENTO_TERMO).writePermission(CADASTRAR_GERENCIAMENTO_TERMO).associated(ImmutableList.of()).build())
            .put(LicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_LICITACAO).writePermission(CADASTRAR_LICITACAO).associated(ImmutableList.of(LISTAR_BOARD_LICITACAO_VIEW, LISTAR_ENTIDADE, LISTAR_GERENCIAMENTO_TERMO, LISTAR_TIPO_LICITACAO, LISTAR_FORMA_LICITACAO, LISTAR_COMISSAO, LISTAR_FONTE_RECURSO, LISTAR_OCORRENCIA_LICITACAO, LISTAR_PUBLICACAO, LISTAR_PARAM_ARQUIVO, LISTAR_PARECERISTA, CADASTRAR_PARECERISTA, LISTAR_FORMA_PUBLICACAO, LISTAR_ARQUIVO_LICITACAO, CADASTRAR_LICITANTE, LISTAR_EDIFICACAO, LISTAR_OBRA_CATEGORIA, LISTAR_OBRA_TIPO, LISTAR_MODALIDADE_LICITACAO, LISTAR_CONTRATO, LISTAR_ADITIVO_CONTRATO, LISTAR_BOARD_LICITACAO_VIEW, LISTAR_ATOS_DIARIO_OFICIAL_LICITACAO)).build())
            .put(LicitacaoSrpViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_LICITACAO_SRP_VIEW).build())
            .put(LicitanteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_LICITANTE).writePermission(CADASTRAR_LICITANTE).build())
            .put(LinkConsultaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GERADOR_LINKS).writePermission(CADASTRAR_GERADOR_LINKS).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_FONTE_RECURSO)).build())
            .put(MapeamentoAtosLicitacoesViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_MAPEAMENTO_ATOS_LICITACOES).writePermission(CADASTRAR_MAPEAMENTO_ATOS_LICITACOES).associated(ImmutableList.of(CADASTRAR_ATOS_DIARIO_OFICIAL_LICITACAO, LISTAR_LICITACAO)).build())
            .put(MaterialController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONSULTA_MATERIAIS).associated(ImmutableList.of(LISTAR_GRUPO, LISTAR_UNIDADE_MEDIDA, LISTAR_CLASSE, LISTAR_DIVISAO, LISTAR_PDM_CARACTERISTICA, LISTAR_PDM, LISTAR_SECAO_CATALOGO, LISTAR_SUB_CLASSE)).build())
            .put(ModalidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_MODALIDADE_LICITACAO).writePermission(CADASTRAR_MODALIDADE_LICITACAO).build())
            .put(MonitoramentoAtosDiarioOficialViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_MONITORAMENTO_ATOS_DIARIO_OFICIAL).associated(ImmutableList.of(LISTAR_ATOS_DIARIO_OFICIAL_LICITACAO)).build())
            .put(MunicipioController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_MUNICIPIO).writePermission(CADASTRAR_MUNICIPIO).build())
            .put(ObjetoAnaliseAutomaticaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PARAM_ANALISE_AUTOMATICA).writePermission(CADASTRAR_PARAM_ANALISE_AUTOMATICA).build())
            .put(ObraController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_OBRA).writePermission(CADASTRAR_OBRA).build())
            .put(ObraCategoriaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_OBRA_CATEGORIA).writePermission(CADASTRAR_OBRA_CATEGORIA).build())
            .put(ObraEdificacaoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_OBRA_EDIFICACAO_VIEW).associated(ImmutableList.of(LISTAR_OBRA_MEDICAO)).build())
            .put(ObraMedicaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_OBRA_MEDICAO).writePermission(CADASTRAR_OBRA_MEDICAO).associated(ImmutableList.of(CADASTRAR_OBRA, LISTAR_OBRA_EDIFICACAO_VIEW, LISTAR_PARAM_ARQUIVO, REQUISICAO_MODIFICACAO)).build())
            .put(ObraTipoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_OBRA_TIPO).writePermission(CADASTRAR_OBRA_TIPO).build())
            .put(ObrigatoriedadeArquivoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PARAM_ARQUIVO).writePermission(CADASTRAR_PARAM_ARQUIVO).build())
            .put(OcorrenciaLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_OCORRENCIA_LICITACAO).writePermission(CADASTRAR_OCORRENCIA_LICITACAO).associated(ImmutableList.of(LISTAR_ARQUIVO_OCORRENCIA_LICITACAO)).build())
            .put(PareceristaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PARECERISTA).writePermission(CADASTRAR_PARECERISTA).build())
            .put(PdmCaracteristicaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PDM_CARACTERISTICA).build())
            .put(PdmController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PDM).build())
            .put(PessoaComissaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PESSOA_COMISSAO).writePermission(CADASTRAR_PESSOA_COMISSAO).build())
            .put(PermissaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PERMISSAO).writePermission(CADASTRAR_PERMISSAO).build())
            .put(PoderController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PODER).writePermission(CADASTRAR_PODER).build())
            .put(ProcessoViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONSULTA_PROCESSOS).associated(ImmutableList.of(LISTAR_LICITACAO, LISTAR_ENTIDADE, LISTAR_EDIFICACAO, LISTAR_CONTRATO)).build())
            .put(PublicacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_PUBLICACAO).writePermission(CADASTRAR_PUBLICACAO).build())
            .put(RelatorioObraController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_RELATORIO_OBRA).writePermission(CADASTRAR_RELATORIO_OBRA).build())
            .put(CatalogoSicroSinapiController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_RELATORIO_OBRA).writePermission(CADASTRAR_RELATORIO_OBRA).build())
            .put(RequisicaoModificacaoController.class.getName(), PermissionCapsule.builder().writePermission(REQUISICAO_MODIFICACAO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_INEXIGIBILIDADE, LISTAR_DISPENSA, LISTAR_LICITACAO, LISTAR_PUBLICACAO, LISTAR_OCORRENCIA_LICITACAO)).build())
            .put(ResponsavelEnteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_RESPONSAVEL_ENTE).writePermission(CADASTRAR_RESPONSAVEL_ENTE).associated(ImmutableList.of(LISTAR_ENTE)).build())
            .put(RiscoEntidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_RISCO_ENTIDADE).writePermission(CADASTRAR_RISCO_ENTIDADE).build())
            .put(SecaoCatalogoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_SECAO_CATALOGO).build())
            .put(SecaoChecklistController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_SECAO_CHECKLIST).writePermission(CADASTRAR_SECAO_CHECKLIST).associated(ImmutableList.of(LISTAR_LICITACAO, LISTAR_CARONA, LISTAR_INEXIGIBILIDADE, LISTAR_CREDENCIAMENTO, LISTAR_DISPENSA, CADASTRAR_TDA_LICITACAO, CADASTRAR_TDA_CARONA, CADASTRAR_TDA_DISPENSA, CADASTRAR_TDA_INEXIGIBILIDADE, CADASTRAR_TDA_CREDENCIAMENTO, CADASTRAR_CHECKLIST_LICITACAO, CADASTRAR_CHECKLIST_DISPENSA, CADASTRAR_CHECKLIST_CARONA, CADASTRAR_CHECKLIST_INEXIGIBILIDADE, CADASTRAR_CHECKLIST_CREDENCIAMENTO)).build())
            .put(SentencaEditalController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_SENTENCA_EDITAL).writePermission(CADASTRAR_SENTENCA_EDITAL).build())
            .put(StatusSistemaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_SISTEMA).writePermission(CADASTRAR_SISTEMA).build())
            .put(SubClasseController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_SUB_CLASSE).build())
            .put(TdaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TDA).writePermission(CADASTRAR_TDA).associated(ImmutableList.of(CADASTRAR_TDA_CARONA, LISTAR_TDA_CARONA, CADASTRAR_TDA_DISPENSA, LISTAR_TDA_DISPENSA, CADASTRAR_TDA_INEXIGIBILIDADE, LISTAR_TDA_INEXIGIBILIDADE, CADASTRAR_TDA_LICITACAO, LISTAR_TDA_LICITACAO, LISTAR_USUARIO, LISTAR_USUARIO_AUDITOR, CADASTRAR_TDA_CREDENCIAMENTO, LISTAR_TDA_CREDENCIAMENTO)).build())
            .put(TdaCaronaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TDA_CARONA).writePermission(CADASTRAR_TDA_CARONA).build())
            .put(TdaDispensaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TDA_DISPENSA).writePermission(CADASTRAR_TDA_DISPENSA).build())
            .put(TdaInexigibilidadeController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TDA_INEXIGIBILIDADE).writePermission(CADASTRAR_TDA_INEXIGIBILIDADE).build())
            .put(TdaLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TDA_LICITACAO).writePermission(CADASTRAR_TDA_LICITACAO).build())
            .put(TermoReferenciaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GERENCIAMENTO_TERMO).writePermission(CADASTRAR_GERENCIAMENTO_TERMO).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_CONSULTA_MATERIAIS, LISTAR_GRUPO, LISTAR_UNIDADE_MEDIDA, LISTAR_CLASSE, LISTAR_DIVISAO, LISTAR_PDM_CARACTERISTICA, LISTAR_PDM, LISTAR_SECAO_CATALOGO, LISTAR_SUB_CLASSE)).build())
            .put(ItemLicitanteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ITEM_LICITANTE).writePermission(CADASTRAR_ITEM_LICITANTE).build())
            .put(ItemLoteController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GERENCIAMENTO_TERMO).writePermission(CADASTRAR_GERENCIAMENTO_TERMO).build())
            .put(TermoReferenciaSecaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_GERENCIAMENTO_TERMO).writePermission(CADASTRAR_GERENCIAMENTO_TERMO).build())
            .put(TipoLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TIPO_LICITACAO).writePermission(CADASTRAR_TIPO_LICITACAO).build())
            .put(UsuarioAuditorViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_USUARIO_AUDITOR_VIEW).build())
            .put(UsuarioController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_USUARIO).writePermission(CADASTRAR_USUARIO).associated(ImmutableList.of(LISTAR_GRUPO_USUARIO, LISTAR_USUARIO_AUDITOR, CADASTRAR_GRUPO_USUARIO)).build())
            .put(VariavelControleController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_VARIAVEL_CONTROLE).writePermission(CADASTRAR_VARIAVEL_CONTROLE).build())
            .put(VencedorLicitacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_VENCEDOR_LICITACAO).writePermission(CADASTRAR_VENCEDOR_LICITACAO).associated(ImmutableList.of(LISTAR_ITEM_LICITANTE)).build())
            .put(EntidadeInternaExternaController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ENTIDADE).associated(ImmutableList.of(LISTAR_ENTIDADE_EXTERNA)).build())
            .put(SelecioneAnaliseAuditoriaController.class.getName(), PermissionCapsule.builder().writePermission(SELECIONE_ANALISE_AUDITORIA).build())
            .put(FornecedoresContratadosViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_FORNECEDORES_CONTRATADOS_VIEW).build())
            .put(TipoProcessoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TIPO_PROCESSO).writePermission(CADASTRAR_TIPO_PROCESSO).build())
            .put(AlertasAuditoriaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ALERTAS_VIEW).build())
            .put(ConfiguracoesEmailController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONFIGURACOES_EMAIL).writePermission(CADASTRAR_CONFIGURACOES_EMAIL).build())
            .put(GeoobraObraController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_OBRA).writePermission(GEOOBRA_CADASTRAR_OBRA).build())
            .put(UsuarioDiretorViewController.class.getName(), PermissionCapsule.builder().readPermission(ANALISAR_ALERTA_PENDENTE).build())
            .put(CredenciamentoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CREDENCIAMENTO).writePermission(CADASTRAR_CREDENCIAMENTO).associated(ImmutableList.of(CADASTRAR_ANULACAO_REVOGACAO)).build())
            .put(AcompanhamentoObraViewController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_ACOMPANHAMENTO_OBRA).associated(ImmutableList.of(LISTAR_PARAM_ARQUIVO)).build())
            .put(MapaViewController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_OBRA).associated(ImmutableList.of(LISTAR_MUNICIPIO)).build())
            .put(MapaView3DController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_MAPA_3D).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_MUNICIPIO)).build())
            .put(MedicaoController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_ACOMPANHAMENTO_OBRA).writePermission(GEOOBRA_CADASTRAR_ACOMPANHAMENTO_OBRA).build())
            .put(ContratoObraViewController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_OBRA).build())
            .put(ArquivoMedicaoController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_ACOMPANHAMENTO_OBRA).writePermission(GEOOBRA_CADASTRAR_ACOMPANHAMENTO_OBRA).build())
            .put(DiarioObraController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_ACOMPANHAMENTO_OBRA).writePermission(GEOOBRA_CADASTRAR_ACOMPANHAMENTO_OBRA).build())
            .put(ComplexoObraController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_COMPLEXO_OBRA).writePermission(GEOOBRA_CADASTRAR_COMPLEXO_OBRA).build())
            .put(DenunciaObraController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_DENUNCIA_OBRA).associated(ImmutableList.of(GEOOBRA_LISTAR_OBRA)).build())
            .put(CredenciadoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CREDENCIADO).writePermission(CADASTRAR_CREDENCIADO).associated(ImmutableList.of(LISTAR_CREDENCIADO_ITEM)).build())
            .put(CredenciadoItemController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CREDENCIADO_ITEM).writePermission(CADASTRAR_CREDENCIADO_ITEM).associated(ImmutableList.of(LISTAR_ITEM_LICITANTE)).build())
            .put(PainelAtosMateiroViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ATOS_LICITACAO_MATEIRO).build())
            .put(ChecklistCredenciamentoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CHECKLIST_CREDENCIAMENTO).writePermission(CADASTRAR_CHECKLIST_CREDENCIAMENTO).build())
            .put(TdaCredenciamentoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_TDA_CREDENCIAMENTO).writePermission(CADASTRAR_TDA_CREDENCIAMENTO).build())
            .put(ArquivoAnulacaoRevogacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ARQUIVOS_ANULACAO_REVOGACAO).writePermission(CADASTRAR_ARQUIVOS_ANULACAO_REVOGACAO).build())
            .put(AnulacaoRevogacaoController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_ANULACAO_REVOGACAO).writePermission(CADASTRAR_ANULACAO_REVOGACAO).build())
            .put(CaronaEntidadeTransferidaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CARONA).writePermission(EDITAR_ENTIDADE_TRANSFERIDA).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_USUARIO, LISTAR_TIPO_LICITACAO, LISTAR_FONTE_RECURSO, LISTAR_LICITANTE, LISTAR_ARQUIVO_CARONA, LISTAR_CARONA_LICITANTE, LISTAR_ENTIDADE_EXTERNA, LISTAR_MUNICIPIO, LISTAR_PARAM_ARQUIVO, LISTAR_VENCEDOR_LICITACAO, LISTAR_LICITACAO, LISTAR_GERENCIAMENTO_TERMO, REQUISICAO_MODIFICACAO, CADASTRAR_ENTIDADE_EXTERNA, CADASTRAR_LICITANTE, LISTAR_LICITACAO_SRP_VIEW, LISTAR_ENTIDADE_TRANSFERIDA)).build())
            .put(DispensaEntidadeTransferidaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_DISPENSA).writePermission(EDITAR_ENTIDADE_TRANSFERIDA).associated(ImmutableList.of(LISTAR_FONTE_RECURSO, LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_ARQUIVO_DISPENSA, LISTAR_DISPENSA_LICITANTE, LISTAR_FUNDAMENTACAO_LEGAL, LISTAR_PARAM_ARQUIVO, LISTAR_GERENCIAMENTO_TERMO, REQUISICAO_MODIFICACAO, CADASTRAR_LICITANTE, LISTAR_USUARIO, LISTAR_ENTIDADE_TRANSFERIDA)).build())
            .put(InexigibilidadeEntidadeTransferidaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_INEXIGIBILIDADE).writePermission(EDITAR_ENTIDADE_TRANSFERIDA).associated(ImmutableList.of(LISTAR_FONTE_RECURSO, LISTAR_LICITANTE, LISTAR_ARQUIVO_INEXIGIBILIDADE, LISTAR_FUNDAMENTACAO_LEGAL, LISTAR_INEXIGIBILIDADE_LICITANTE, LISTAR_ENTIDADE, LISTAR_PARAM_ARQUIVO, REQUISICAO_MODIFICACAO, LISTAR_GERENCIAMENTO_TERMO, CADASTRAR_LICITANTE, LISTAR_ENTIDADE_TRANSFERIDA)).build())
            .put(ContratoEntidadeTransferidaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CONTRATO).writePermission(EDITAR_ENTIDADE_TRANSFERIDA).associated(ImmutableList.of(LISTAR_ENTIDADE, LISTAR_LICITANTE, LISTAR_USUARIO, LISTAR_FONTE_RECURSO, LISTAR_ELEMENTO_DESPENSA, LISTAR_VENCEDOR_LICITACAO, LISTAR_PROCESSO_VIEW, REQUISICAO_MODIFICACAO, RESCISAO_CONTRATUAL, LISTAR_ADITIVO_CONTRATO, LISTAR_PARAM_ARQUIVO, LISTAR_INEXIGIBILIDADE, LISTAR_CONSULTA_PROCESSOS, CADASTRAR_INEXIGIBILIDADE, LISTAR_MODALIDADE_LICITACAO, CADASTRAR_CARONA, CADASTRAR_DISPENSA, CADASTRAR_LICITACAO, LISTAR_ENTIDADE_EXTERNA, LISTAR_CREDENCIADO_ITEM, LISTAR_ENTIDADE_TRANSFERIDA)).build())
            .put(CredenciamentoEntidadeTransferidaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_CREDENCIAMENTO).writePermission(EDITAR_ENTIDADE_TRANSFERIDA).associated(ImmutableList.of(LISTAR_ENTIDADE_TRANSFERIDA)).build())
            .put(LicitacaoEntidadeTransferidaViewController.class.getName(), PermissionCapsule.builder().readPermission(LISTAR_LICITACAO).writePermission(EDITAR_ENTIDADE_TRANSFERIDA).associated(ImmutableList.of(LISTAR_BOARD_LICITACAO_VIEW, LISTAR_ENTIDADE, LISTAR_GERENCIAMENTO_TERMO, LISTAR_TIPO_LICITACAO, LISTAR_FORMA_LICITACAO, LISTAR_COMISSAO, LISTAR_FONTE_RECURSO, LISTAR_OCORRENCIA_LICITACAO, LISTAR_PUBLICACAO, LISTAR_PARAM_ARQUIVO, LISTAR_PARECERISTA, CADASTRAR_PARECERISTA, LISTAR_FORMA_PUBLICACAO, LISTAR_ARQUIVO_LICITACAO, CADASTRAR_LICITANTE, LISTAR_EDIFICACAO, LISTAR_OBRA_CATEGORIA, LISTAR_OBRA_TIPO, LISTAR_MODALIDADE_LICITACAO, LISTAR_CONTRATO, LISTAR_ADITIVO_CONTRATO, LISTAR_BOARD_LICITACAO_VIEW, LISTAR_ATOS_DIARIO_OFICIAL_LICITACAO, LISTAR_ENTIDADE_TRANSFERIDA)).build())
            .put(SituacaoObraController.class.getName(), PermissionCapsule.builder().readPermission(GEOOBRA_LISTAR_ACOMPANHAMENTO_OBRA).writePermission(GEOOBRA_CADASTRAR_ACOMPANHAMENTO_OBRA).build())
            .build();

    /**
     * Método responsável por retornar o valor PermissionCapsule referente a chave classString informada.
     *
     * @param classString nome do controller utilizada como chave no mapa permissionMapping
     * @return a cápsula de permissões encontrada, caso exista, senão uma cápsula vazia
     */
    public PermissionCapsule getPermissionCapsule(String classString) {
        if (permissionMapping.keySet().contains(classString)) {
            return permissionMapping.get(classString);
        } else {
            return PermissionCapsule.builder().writePermission("").readPermission("").build();
        }
    }

    /**
     * Método responsável por retornar uma string concatada por virgula com as permissões de escrita para a chave
     * classString informada. Sempre concatenando a permissão de admin.
     *
     * @param classString nome do controller utilizada como chave no mapa permissionMapping
     * @return string com as permissões
     */
    public String getWritePermission(String classString) {
        StringBuilder permissions = new StringBuilder();
        permissions.append(this.getPermissionCapsule(classString).getWritePermission()).append(", admin");
        return permissions.toString();
    }

    /**
     * Método responsável por retornar uma string concatada por virgula com as permissões de leitura para a chave
     * classString informada. Sempre concatenando a permissão de admin.
     *
     * @param classString nome do controller utilizada como chave no mapa permissionMapping
     * @return string com as permissões
     */
    public String getReadPermission(String classString) {
        StringBuilder permissions = new StringBuilder();
        permissions.append(this.getPermissionCapsule(classString).getReadPermission()).append(", ");
        permissions.append(this.getPermissionCapsule(classString).getWritePermission()).append(", admin");
        return permissions.toString();
    }

    private List<String> getContextPermissionsList() {
        String permissionsString = ThreadContext.get("permissions");
        permissionsString = permissionsString.replace("[", "").replace("]", "");
        return Arrays.asList(permissionsString.split(", "));
    }

    /**
     * Retorna as Authorities referentes as permissões de usuário definidas no ThreadContext.
     *
     * @return Set contendo as Authorities
     */
    public Set<String> getAuthorities() {
        List<String> userPermissions = this.getContextPermissionsList();
        Set<String> authorities = new HashSet<>();
        authorities.addAll(userPermissions);

        for (String classPermission : permissionMapping.keySet()) {
            if (userPermissions.contains(permissionMapping.get(classPermission).getReadPermission())
                    || userPermissions.contains(permissionMapping.get(classPermission).getWritePermission())) {
                PermissionCapsule permissionCapsule = permissionMapping.get(classPermission);
                if (permissionCapsule.getAssociated() != null) {
                    authorities.addAll(permissionCapsule.getAssociated());
                }
            }
        }
        return authorities;
    }
}
